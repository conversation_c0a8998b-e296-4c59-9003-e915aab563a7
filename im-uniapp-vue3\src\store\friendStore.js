import { defineStore } from 'pinia';
import http from '../common/request'
import { TERMINAL_TYPE } from '../common/enums.js'

export default defineStore('friendStore', {
	state: () => {
		return {
			friends: [],
			timer: null
		}
	},
	actions: {
		setFriends(friends) {
			// 创建一个 Map 来存储唯一的好友，以好友 ID 为键
			const uniqueFriendsMap = new Map();

			// 遍历所有好友，只保留每个 ID 的最新版本
			friends.forEach((f) => {
				f.online = false;
				f.onlineWeb = false;
				f.onlineApp = false;
				uniqueFriendsMap.set(f.id, f);
			});

			// 将 Map 转换回数组
			this.friends = Array.from(uniqueFriendsMap.values());
		},
		updateFriend(friend) {
			let f = this.findFriend(friend.id);
			let copy = JSON.parse(JSON.stringify(f));
			Object.assign(f, friend);
			f.online = copy.online;
			f.onlineWeb = copy.onlineWeb;
			f.onlineApp = copy.onlineApp;
		},
		removeFriend(id) {
			this.friends.forEach((f, idx) => {
				if (f.id == id) {
					this.friends.splice(idx, 1)
				}
			})
		},
		addFriend(friend) {
			// 检查是否已存在相同 ID 的好友，避免重复添加
			const existingIndex = this.friends.findIndex(f => f.id === friend.id);
			if (existingIndex !== -1) {
				// 如果已存在，则更新该好友信息
				this.friends[existingIndex] = { ...this.friends[existingIndex], ...friend };
			} else {
				// 如果不存在，则添加新好友
				this.friends.push(friend);
			}
		},
		setOnlineStatus(onlineTerminals) {
			this.friends.forEach((f) => {
				let userTerminal = onlineTerminals.find((o) => f.id == o.userId);
				if (userTerminal) {
					f.online = true;
					f.onlineWeb = userTerminal.terminals.indexOf(TERMINAL_TYPE.WEB) >= 0
					f.onlineApp = userTerminal.terminals.indexOf(TERMINAL_TYPE.APP) >= 0
				} else {
					f.online = false;
					f.onlineWeb = false;
					f.onlineApp = false;
				}
			});
		},
		refreshOnlineStatus() {
			if (this.friends.length > 0) {
				let userIds = [];
				this.friends.forEach(f => userIds.push(f.id));
				http({
					url: '/user/terminal/online?userIds=' + userIds.join(','),
					method: 'GET'
				}).then((onlineTerminals) => {
					this.setOnlineStatus(onlineTerminals);
				})
			}
			// 30s后重新拉取
			clearTimeout(this.timer);
			this.timer = setTimeout(() => {
				this.refreshOnlineStatus();
			}, 30000)
		},
		clear() {
			clearTimeout(this.timer);
			this.friends = [];
			this.timer = null;
		},
		loadFriend() {
			return new Promise((resolve, reject) => {
				http({
					url: '/friend/list',
					method: 'GET'
				}).then((friends) => {
					this.setFriends(friends);
					this.refreshOnlineStatus();
					resolve()
				}).catch((res) => {
					reject();
				})
			});
		}
	},
	getters: {
		findFriend: (state) => (id) => {
			return state.friends.find((f) => f.id == id);
		}
	}
})