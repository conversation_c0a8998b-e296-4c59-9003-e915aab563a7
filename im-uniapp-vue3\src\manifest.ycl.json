{
    "name": "云畅聊",
    "appid": "__UNI__C25CCD6",
    "description": "",
    "versionName": "1.0.6",
    "versionCode": 1002,
    "transformPx": false,
    /* 5+App特有相关 */
    "app-plus": {
        "safearea": {
            //iOS平台的安全区域
            "background": "#ffffff",
            "backgroundDark": "#131D27", // HX 3.1.19+支持
            "bottom": {
                "offset": "auto"
            }
        },
        "darkmode": true,
        "themeLocation": "theme.json", // 深色模式JSON文件，如果 theme.json 在根目录可省略
        "usingComponents": true,
        "nvueStyleCompiler": "uni-app",
        "compilerVersion": 3,
        "splashscreen": {
            "alwaysShowBeforeRender": true,
            "waiting": true,
            "autoclose": true,
            "delay": 0
        },
        /* 模块配置 */
        "modules": {
            "Camera": {},
            "Record": {},
            "VideoPlayer": {},
            "Push": {},
            "Barcode": {}
        },
        "softinput": {
            "mode": "adjustResize"
        },
        /* 应用发布信息 */
        "distribute": {
            /* android打包配置 */
            "android": {
                "permissions": [
                    // "<uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\" />",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\" />"
                ],
                "abiFilters": [
                    "armeabi-v7a",
                    "arm64-v8a",
                    "x86"
                ],
                "minSdkVersion": 21,
                "targetSdkVersion": 31,
                "pushRegisterMode": "manual",
                "schemes": "chuangxin"
            },
            /* ios打包配置 */
            "ios": {
                "dSYMs": false,
                "privacyDescription": {
                    "NSMicrophoneUsageDescription": "请允许使用麦克风，以便于您进行录音、语音通话、视频通话",
                    "NSCameraUsageDescription": "请允许使用摄像头，以便于您进行拍照、语音通话、视频通话",
                    "NSPhotoLibraryUsageDescription": "请允许访问所有照片，以便更自由地上传、下载、分享",
                    "NSPhotoLibraryAddUsageDescription": "请允许保存图片到相册，以便更自由地上传、下载、分享",
                    "NSUserTrackingUsageDescription": "请放心，开启权限不会获取您在其他站点的隐私信息，该权限仅用于标识设备并保障服务安全与提示浏览体验",
                    "NSLocalNetworkUsageDescription": "请允许访问本地网络，以便为您提供更好的服务体验"
                },
                "idfa": true,
                "urltypes": "chuangxin"
            },
            /* SDK配置 */
            "sdkConfigs": {
                "ad": {},
                "speech": {},
                "push": {
                    "unipush": {}
                }
            },
            "icons": {
                "android": {
                    "xhdpi": "unpackage/res/icons/ycl/96x96.png",
                    "hdpi": "unpackage/res/icons/ycl/72x72.png",
                    "xxhdpi": "unpackage/res/icons/ycl/144x144.png",
                    "xxxhdpi": "unpackage/res/icons/ycl/192x192.png"
                },
                "ios": {
                    "appstore": "unpackage/res/icons/ycl/1024x1024.png",
                    "ipad": {
                        "app": "unpackage/res/icons/ycl/76x76.png",
                        "app@2x": "unpackage/res/icons/ycl/152x152.png",
                        "notification": "unpackage/res/icons/ycl/20x20.png",
                        "notification@2x": "unpackage/res/icons/ycl/40x40.png",
                        "proapp@2x": "unpackage/res/icons/ycl/167x167.png",
                        "settings": "unpackage/res/icons/ycl/29x29.png",
                        "settings@2x": "unpackage/res/icons/ycl/58x58.png",
                        "spotlight": "unpackage/res/icons/ycl/40x40.png",
                        "spotlight@2x": "unpackage/res/icons/ycl/80x80.png"
                    },
                    "iphone": {
                        "app@2x": "unpackage/res/icons/ycl/120x120.png",
                        "app@3x": "unpackage/res/icons/ycl/180x180.png",
                        "notification@2x": "unpackage/res/icons/ycl/40x40.png",
                        "notification@3x": "unpackage/res/icons/ycl/60x60.png",
                        "settings@2x": "unpackage/res/icons/ycl/58x58.png",
                        "settings@3x": "unpackage/res/icons/ycl/87x87.png",
                        "spotlight@2x": "unpackage/res/icons/ycl/80x80.png",
                        "spotlight@3x": "unpackage/res/icons/ycl/120x120.png"
                    }
                }
            },
            "splashscreen": {
                "useOriginalMsgbox": true
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp": {},
    /* 小程序特有相关 */
    "mp-weixin": {
        "appid": "wxda94f40bfad0262c",
        "libVersion": "latest",
        "setting": {
            "urlCheck": false
        },
        "usingComponents": true
    },
    "mp-alipay": {
        "usingComponents": true
    },
    "mp-baidu": {
        "usingComponents": true
    },
    "mp-toutiao": {
        "usingComponents": true
    },
    "uniStatistics": {
        "enable": false
    },
    "vueVersion": "3",
    "h5": {
        "darkmode": true,
        "title": "云畅聊",
        "router": {
            "base": "/"
        }
    },
    "locale": "zh-Hans"
}
/* ios打包配置 */ /* SDK配置 */