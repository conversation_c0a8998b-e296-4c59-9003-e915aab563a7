import request from '@/common/request';

// 查询群聊信息
export const reqGroupFind = (id) => {
  return request({
    url: `/group/find/${id}`,
    method: 'get',
  });
};

// 查询群聊成员
export const reqGroupMembers = (id) => {
  return request({
    url: `/group/members/${id}`,
    method: 'get',
  });
};

// 查询群聊成员 - 分页
export const reqPageGroupMembers = (id, data) => {
  return request({
    url: `/group/members/page/${id}`,
    method: 'post',
    data
  });
};

// 群主转让
export const reqGroupOwnerTransfer = (data) => {
  return request({
    url: `/group/groupOwnerTransfer`,
    method: 'post',
    data
  });
};

// 设置群发送链接
export const reqSetGroupSendUrl = (data) => {
  return request({
    url: `/group/setGroupSendUrl`,
    method: 'post',
    data
  });
};

// 设置群发送文件
export const reqSetGroupSendFile = (data) => {
  return request({
    url: `/group/setGroupSendFile`,
    method: 'post',
    data
  });
};

// 设置群互加好友
export const reqSetGroupAddFriend = (data) => {
  return request({
    url: `/group/setGroupAddFriend`,
    method: 'post',
    data
  });
};

// 设置群成员邀请
export const reqSetGroupInvite = (data) => {
  return request({
    url: `/group/setGroupInvite`,
    method: 'post',
    data
  });
};

// 设置群邀请校验
export const reqSetGroupInviteCheck = (data) => {
  return request({
    url: `/group/setGroupInviteCheck`,
    method: 'post',
    data
  });
};

// 设置允许修改群昵称
export const reqSetGroupUpdateName = (data) => {
  return request({
    url: `/group/setGroupUpdateName`,
    method: 'post',
    data
  });
};

// 修改群昵称
export const reqSetGroupNickName = (data) => {
  return request({
    url: `/group/setGroupNickName`,
    method: 'post',
    data
  });
};

// 设置群管理员
export const reqSetGroupAdmin = (data) => {
  return request({
    url: `/group/setGroupAdmin`,
    method: 'post',
    data
  });
};

// 修改群公告
export const reqUpdateNotice = (data) => {
  return request({
    url: `/group/updateNotice`,
    method: 'post',
    data
  });
};

// 一键复制群聊
export const reqGroupCopy = (data) => {
  return request({
    url: `/group/copy`,
    method: 'post',
    data
  });
};

// 获取新群组申请列表（支持分页）
export function reqNewGroups({ page = 1, size = 10 } = {}) {
  return request({
    url: "/groupMember/findGroupAddPage",
    method: "post",
    data: {
      page,
      size
    }
  });
}

// 通过群申请
export function reqPassGroupAdd(id) {
  return request({
    url: `/groupMember/passGroupAdd/${id}`,
    method: "POST"
  });
}

// 拒绝群申请
export function reqRejectGroupAdd(id) {
  return request({
    url: `/groupMember/noPassGroupAdd/${id}`,
    method: "POST"
  });
}

// 二维码查询群组信息
export function reqQRCodeGroupFind(data) {
  return request({
    url: `/group/qRCodeGroupFind`,
    method: "POST",
    data
  });
}

// 扫二维码加群
export function reqQRCodeGroupAdd(data) {
  return request({
    url: `/group/qRCodeGroupAdd`,
    method: "POST",
    data
  });
}
