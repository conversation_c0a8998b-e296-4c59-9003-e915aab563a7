import CryptoJS from 'crypto-js';
import { pinyin } from "pinyin-pro";

import CONFIG from '@/configs';

// 判断能否使用JSON.parse方法
export function isCanParse(str) {
  if (typeof str !== 'string') return false;
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

// 获取JSON.parse后的值
// needRecursion 是否需要递归获取数据最原始类型，默认不需要
export const getJSONParse = (param, needRecursion = false) => {
  // 不是字符串类型，返回空字符串
  if (typeof param !== 'string') return '';
  // 清除JSON字符串中的换行符
  param = param.replace(/\n+/gi, '');
  // 如果引号都是\\"这样的格式才转换成"
  if (param.match(/\\"/g)?.length === param.match(/"/g)?.length) {
    param = param.replace(/\\"/gi, '"');
  }
  // 处理多级嵌套JSON字符串
  if (/\\\\"/.test(param)) {
    param = param.replace(/\\\\"/gi, '\\"');
  }
  try {
    let res = JSON.parse(param);
    // 需要递归获取最原始数据，则继续对返回的字符串进行parse
    if (typeof res === 'string' && needRecursion) {
      return getJSONParse(res);
    } else {
      return res;
    }
  } catch (e) {
    // console.log('e', e);
    return param;
  }
};

// 判断是否是英文
export function isEnglish(character) {
  return /^[A-Za-z]+$/.test(character);
};

// 获取格式化的金额
export function getFormatMoney(num) {
  if (num === undefined || num === null) return '';
  if (typeof num !== 'number') {
    num = parseFloat(num);
  }
  if (isNaN(num)) return '';
  // 保留两位小数
  num = num.toFixed(2);
  return num;
  // 使用正则表达式添加千分位逗号
  // return num.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 使用pinyin-pro库将中文转换为拼音
export function firstLetter(strText) {
  let pinyinOptions = {
    toneType: "none", // 无声调
    type: "normal", // 普通拼音
  };
  let pyText = pinyin(strText, pinyinOptions);
  return pyText[0];
}

// 按首字母分组
export function getGroupMap(object = {}) {
  // 源数组
  const sourceArray = object.sourceArray || [];
  // 搜索字符串
  const searchText = object.searchText?.value || object.searchText || '';
  // 是否给在线/离线分组
  const isOnline = object.isOnline || false;

  let groupMap = new Map();
  sourceArray.forEach((f) => {
    if (searchText && !f.showNickName.includes(searchText)) {
      return;
    }
    let letter = firstLetter(f.showNickName).toUpperCase();
    // 非英文一律为#组
    if (!isEnglish(letter)) {
      letter = '#';
    }
    // 是否给在线/离线分组
    if (isOnline && f.online) {
      letter = "*";
    }
    if (groupMap.has(letter)) {
      groupMap.get(letter).push(f);
    } else {
      groupMap.set(letter, [f]);
    }
  });
  // 排序
  let arrayObj = Array.from(groupMap);
  arrayObj.sort((a, b) => {
    // #组在最后面
    if (a[0] == '#' || b[0] == '#') {
      return b[0].localeCompare(a[0]);
    }
    return a[0].localeCompare(b[0]);
  });
  groupMap = new Map(arrayObj.map((i) => [i[0], i[1]]));
  return groupMap;
}

// 传入一个对象，将参数值未定义的过滤并返回
export function getValidParamObject(obj) {
  const result = {};
  for (let i in obj) {
    if (obj[i] !== undefined) {
      result[i] = obj[i];
    }
  }
  return JSON.parse(JSON.stringify(result));
}

export function decryptAes(hexString) {
  // 如果传入的是对象，则直接返回
  if (Object.prototype.toString.call(hexString) === '[object Object]') return hexString;
  if (/.*[\u4e00-\u9fa5]+.*$/.test(hexString)) return hexString;
  const base64Key = CONFIG.base64Key; // 替换为你的Base64编码密钥
  // 将Base64密钥转换为CryptoJS词语对象
  const key = CryptoJS.enc.Utf8.parse(base64Key);

  // 将十六进制的数据转换为CryptoJS的词语对象
  const encryptedHexStr = CryptoJS.enc.Hex.parse(hexString);
  const encryptedBase64Str = CryptoJS.enc.Base64.stringify(encryptedHexStr);

  // 解密数据
  const decryptedData = CryptoJS.AES.decrypt(encryptedBase64Str, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });

  // 将解密后的数据转换为utf8字符串
  const decryptedText = decryptedData.toString(CryptoJS.enc.Utf8);

  // 不能parse的话，不要继续了
  if (!isCanParse(decryptedText)) return;
  return JSON.parse(decryptedText);
}
